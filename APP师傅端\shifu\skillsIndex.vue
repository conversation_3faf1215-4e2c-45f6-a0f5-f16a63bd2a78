
<template>
  <view class="page">
    <view class="main">
      <view class="left">
        <scroll-view scroll-y="true" class="scrollL">
          <view v-if="loading" class="loading">
            <text>加载中...</text>
          </view>
          <view v-else-if="error" class="error">
            <text>{{ error }}</text>
          </view>
          <view v-else-if="!categories.length" class="no-content">
            <text>暂无分类数据</text>
          </view>
          <view
            v-else
            class="left_item"
            v-for="category in categories"
            :key="category.id"
            @tap="selectCategory(category.id)"
            :class="{ active: selectedCategoryId === category.id }"
          >
            <view class="category_name">{{ category.name }}</view>
          </view>
        </scroll-view>
      </view>

      <view class="right">
        <scroll-view scroll-y="true" class="scrollR">
          <view v-if="loading" class="loading">
            <text>加载中...</text>
          </view>
          <view v-else-if="error" class="error">
            <text>{{ error }}</text>
          </view>
          <view v-else-if="currentCategory && currentCategory.children && currentCategory.children.length">
            <view 
              class="subcategory_section" 
              v-for="subCategory in currentCategory.children" 
              :key="subCategory.id"
            >
              <view class="subcategory_header">
                <view class="subcategory_title" @click="toggleSubCategory(subCategory.id)">
                  {{ subCategory.name }} 
                  <text class="selected_count">(已选择{{ getSelectedCount(subCategory.id) }})</text>
                </view>
                <view class="select_all" @click="selectAllServices(subCategory.id)">
                  {{ isAllSelected(subCategory.id) ? '取消全选' : '全选' }}
                </view>
                <view class="expand_icon" @click="toggleSubCategory(subCategory.id)">
                  {{ expandedSubCategories.includes(subCategory.id) ? '▲' : '▼' }}
                </view>
              </view>
              
              <view 
                class="service_items" 
                v-if="expandedSubCategories.includes(subCategory.id) && subCategory.serviceList && subCategory.serviceList.length"
              >
                <view 
                  class="service_item" 
                  v-for="service in subCategory.serviceList" 
                  :key="service.id"
                  @click="toggleSelectService(service.id, subCategory.id)"
                  :class="{ active: isServiceSelected(service.id, subCategory.id) }"
                >
                  {{ service.title }}
                </view>
              </view>
              
              <view 
                class="no-services" 
                v-else-if="expandedSubCategories.includes(subCategory.id) && (!subCategory.serviceList || !subCategory.serviceList.length)"
              >
                <text>暂无服务项目</text>
              </view>
            </view>
          </view>
          <view v-else class="no-content">
            <text>暂无子分类</text>
          </view>
        </scroll-view>
      </view>
    </view>
    <view class="footer">
      <button 
        class="save_btn" 
        @click="debouncedSaveSettings" 
        :disabled="isSaving"
        :class="{ disabled: isSaving }"
      >
        {{ isSaving ? '保存中...' : '保存设置' }}
      </button>
    </view>
  </view>
</template>

<script>
import $api from "@/api/index.js";

// Debounce function
const debounce = (fn, wait) => {
  let timeout = null;
  return function (...args) {
    if (timeout) clearTimeout(timeout); // Clear existing timeout
    timeout = setTimeout(() => {
      timeout = null;
      fn.apply(this, args);
    }, wait);
  };
};

export default {
  data() {
    return {
      keyword: "",
      categories: [],
      selectedCategoryId: null,
      expandedSubCategories: [], // Stores IDs of expanded subcategories
      loading: false,
      shifuInfo: { serviceIds: [] }, // Initialize with default serviceIds
      error: null,
      dataBox: {}, // Stores user-selected service items, grouped by subcategory ID
      isSaving: false, // Save status
    };
  },
  computed: {
    currentCategory() {
      if (!this.selectedCategoryId) return null;
      const category = this.categories.find(cat => cat.id === this.selectedCategoryId);
      console.log("Current category:", category);
      return category;
    }
  },
  methods: {
    // Select parent category
    selectCategory(id) {
      console.log("选择父类分类:", id);
      this.selectedCategoryId = id;
      
      // Initially expand the first subcategory
      const category = this.categories.find(cat => cat.id === id);
      if (category && category.children && category.children.length > 0) {
        this.expandedSubCategories = [category.children[0].id];
      } else {
        this.expandedSubCategories = []; // Clear if no children
      }
      
      this.$forceUpdate();
    },
    
    // Toggle subcategory expand/collapse state
    toggleSubCategory(subCategoryId) {
      console.log("切换子类展开状态:", subCategoryId);
      const index = this.expandedSubCategories.indexOf(subCategoryId);
      
      if (index === -1) {
        this.expandedSubCategories.push(subCategoryId);
      } else {
        this.expandedSubCategories.splice(index, 1);
      }
    },
    
    // Toggle service item selection state
    toggleSelectService(serviceId, subCategoryId) {
      console.log("切换服务选择状态:", serviceId, subCategoryId);
      
      // Ensure shifuInfo is initialized
      if (!this.shifuInfo) {
        this.shifuInfo = { serviceIds: [] };
      }
      
      // Ensure the subcategory exists in dataBox
      if (!this.dataBox[subCategoryId]) {
        this.$set(this.dataBox, subCategoryId, {
          selectedItems: [],
          count: 0
        });
      }
      
      const index = this.dataBox[subCategoryId].selectedItems.indexOf(serviceId);
      
      if (index === -1) {
        this.dataBox[subCategoryId].selectedItems.push(serviceId);
        if (!this.shifuInfo.serviceIds.includes(serviceId)) {
          this.shifuInfo.serviceIds.push(serviceId);
        }
      } else {
        this.dataBox[subCategoryId].selectedItems.splice(index, 1);
        this.shifuInfo.serviceIds = this.shifuInfo.serviceIds.filter(id => id !== serviceId);
      }
      
      // Update count
      this.dataBox[subCategoryId].count = this.dataBox[subCategoryId].selectedItems.length;
      
      console.log("Updated shifuInfo.serviceIds:", this.shifuInfo.serviceIds);
      this.$forceUpdate();
    },
    
    // Check if service is selected
    isServiceSelected(serviceId, subCategoryId) {
      if (!this.dataBox[subCategoryId]) return false;
      return this.dataBox[subCategoryId].selectedItems.includes(serviceId);
    },
    
    // Get the number of selected services for a subcategory
    getSelectedCount(subCategoryId) {
      if (!this.dataBox[subCategoryId]) return 0;
      return this.dataBox[subCategoryId].count || 0;
    },
    
    // Select all/deselect all service items
    selectAllServices(subCategoryId) {
      const subCategory = this.currentCategory.children.find(sub => sub.id === subCategoryId);
      if (!subCategory || !subCategory.serviceList || !subCategory.serviceList.length) return;

      const allServiceIds = subCategory.serviceList.map(service => service.id);
      const isAllCurrentlySelected = this.isAllSelected(subCategoryId);

      // Ensure dataBox entry exists for this subCategory
      if (!this.dataBox[subCategoryId]) {
        this.$set(this.dataBox, subCategoryId, {
          selectedItems: [],
          count: 0
        });
      }

      if (isAllCurrentlySelected) {
        // Deselect all
        this.dataBox[subCategoryId].selectedItems = [];
        this.shifuInfo.serviceIds = this.shifuInfo.serviceIds.filter(id => !allServiceIds.includes(id));
      } else {
        // Select all
        allServiceIds.forEach(serviceId => {
          if (!this.dataBox[subCategoryId].selectedItems.includes(serviceId)) {
            this.dataBox[subCategoryId].selectedItems.push(serviceId);
          }
          if (!this.shifuInfo.serviceIds.includes(serviceId)) {
            this.shifuInfo.serviceIds.push(serviceId);
          }
        });
      }

      // Update count
      this.dataBox[subCategoryId].count = this.dataBox[subCategoryId].selectedItems.length;
      this.$forceUpdate();
    },
    
    // Check if all service items are selected
    isAllSelected(subCategoryId) {
      const subCategory = this.currentCategory.children.find(sub => sub.id === subCategoryId);
      if (!subCategory || !subCategory.serviceList || !subCategory.serviceList.length) return false;
      const allServiceIds = subCategory.serviceList.map(service => service.id);
      const selectedServiceIds = this.dataBox[subCategoryId]?.selectedItems || [];
      return allServiceIds.length > 0 && allServiceIds.every(id => selectedServiceIds.includes(id));
    },
    
    // Save settings
    async saveSettings() {
      this.isSaving = true; // Set saving status
      try {
        // Save shifuInfo.serviceIds as a comma-separated string
        const serviceIdsString = this.shifuInfo.serviceIds.join(",");
        uni.setStorageSync("selectedServices", serviceIdsString);
        console.log("Saved selectedServices:", serviceIdsString);
        
        // Collect service names for selected serviceIds
        const serviceNames = [];
        this.categories.forEach(category => {
          if (category.children && category.children.length) {
            category.children.forEach(subCategory => {
              if (subCategory.serviceList && subCategory.serviceList.length) {
                subCategory.serviceList.forEach(service => {
                  if (this.shifuInfo.serviceIds.includes(service.id)) {
                    serviceNames.push(service.title);
                  }
                });
              }
            });
          }
        });
        
        // Save service names as a comma-separated string
        const serviceNamesString = serviceNames.join(",");
        uni.setStorageSync("selectedServiceNames", serviceNamesString);
        console.log("Saved selectedServiceNames:", serviceNamesString);
        
        uni.showToast({
          title: "保存成功",
          icon: "success",
          duration: 2000,
          success: () => {
            setTimeout(() => {
              uni.navigateBack({ delta: 1 });
            }, 2000);
          },
        });
      } catch (e) {
        uni.showToast({
          title: "保存失败",
          icon: "none",
        });
        console.error("保存失败:", e);
      } finally {
        this.isSaving = false; // Reset button status
      }
    },
    
    // Debounced saveSettings
    debouncedSaveSettings: null, // Initialized to null, set in created hook
    
    goUrl(url) {
      uni.navigateTo({ url });
    },
    
    // Get category list
    async getList() {
      this.loading = true;
      this.error = null;
      try {
        const responses = await $api.shifu.getNewSkillInfo();
        console.log("API Response:", responses);
		let response =responses.data
        // Process response data
        let categoriesData = [];
        if (Array.isArray(response)) {
          categoriesData = response;
        } else if (response.data && Array.isArray(response.data)) {
          categoriesData = response.data;
        } else {
          throw new Error("无效或空的数据");
        }
        
        // Ensure children and serviceList exist, and initialize dataBox
        categoriesData.forEach(category => {
          if (!category.children) category.children = [];
          category.children.forEach(subCategory => {
            if (!subCategory.serviceList) subCategory.serviceList = [];
            // Only initialize dataBox entry if it doesn't already exist
            if (!this.dataBox[subCategory.id]) {
              this.$set(this.dataBox, subCategory.id, {
                selectedItems: [],
                count: 0,
              });
            }
          });
        });
        
        this.categories = categoriesData;
        console.log("Categories processed:", this.categories);
        
        if (this.categories.length > 0) {
          // If selectedCategoryId is not set (e.g., on initial load), set it to the first category
          if (this.selectedCategoryId === null) {
            this.selectedCategoryId = this.categories[0].id;
          }
          const currentCategory = this.categories.find(cat => cat.id === this.selectedCategoryId);
          if (currentCategory && currentCategory.children && currentCategory.children.length > 0) {
            // Only set expandedSubCategories if it's empty or doesn't include the first child
            if (this.expandedSubCategories.length === 0 || !this.expandedSubCategories.includes(currentCategory.children[0].id)) {
                this.expandedSubCategories = [currentCategory.children[0].id];
            }
          }
        } else {
          this.error = "分类数据为空";
        }
      } catch (err) {
        this.error = "数据加载失败: " + err.message;
        console.error("Error in getList:", err);
      } finally {
        this.loading = false;
      }
    },
    
    // Load saved selections (only as a fallback if API fails)
    loadSavedSelections() {
      try {
        const savedData = uni.getStorageSync('selectedServices');
        if (savedData && savedData.trim()) {
          this.shifuInfo.serviceIds = savedData
            .split(',')
            .map(id => parseInt(id.trim(), 10))
            .filter(id => !isNaN(id));
        } else {
          this.shifuInfo.serviceIds = [];
        }
        
        // Reconstruct dataBox from serviceIds based on the currently loaded categories
        // Ensure categories are loaded before this part is executed effectively
        this.dataBox = {}; // Clear previous dataBox before reconstructing
        this.categories.forEach(category => {
          if (category.children && category.children.length) {
            category.children.forEach(subCategory => {
              if (subCategory.serviceList && subCategory.serviceList.length) {
                this.$set(this.dataBox, subCategory.id, {
                  selectedItems: [],
                  count: 0
                });
                const matchingServiceIds = this.shifuInfo.serviceIds.filter(serviceId =>
                  subCategory.serviceList.some(service => service.id === serviceId)
                );
                this.dataBox[subCategory.id].selectedItems = matchingServiceIds;
                this.dataBox[subCategory.id].count = matchingServiceIds.length;
              }
            });
          }
        });
        
        console.log("Loaded shifuInfo.serviceIds from storage:", this.shifuInfo.serviceIds);
        console.log("Reconstructed dataBox from loaded storage:", this.dataBox);
        this.$forceUpdate();
      } catch (e) {
        console.error('加载已保存选择失败:', e);
        this.shifuInfo.serviceIds = [];
      }
    },
    
    // Get and initialize service IDs
    async getInfoS() {
      try {
        const res = await $api.shifu.getSInfo();
        console.log("getSInfo Response:", res);
        
        // Initialize shifuInfo
        this.shifuInfo = res && typeof res === 'object' ? res : { serviceIds: [] };
        
        // Always use API serviceIds if available
        let serviceIdsArray = [];
        if (typeof this.shifuInfo.serviceIds === 'string' && this.shifuInfo.serviceIds.trim() !== '') {
          serviceIdsArray = this.shifuInfo.serviceIds
            .split(',')
            .map(id => parseInt(id.trim(), 10))
            .filter(id => !isNaN(id));
        } else if (Array.isArray(this.shifuInfo.serviceIds)) {
          serviceIdsArray = this.shifuInfo.serviceIds.map(id => parseInt(id, 10)).filter(id => !isNaN(id));
        }
        this.shifuInfo.serviceIds = serviceIdsArray;
        
        // If API provides no valid serviceIds, try local storage
        if (!this.shifuInfo.serviceIds.length) {
          this.loadSavedSelections();
        }
        
        console.log("Processed Service IDs:", this.shifuInfo.serviceIds);

        // Update dataBox based on shifuInfo.serviceIds after categories are loaded
        this.dataBox = {}; // Clear dataBox to reconstruct based on API data
        this.categories.forEach(category => {
          if (category.children && category.children.length) {
            category.children.forEach(subCategory => {
              if (subCategory.serviceList && subCategory.serviceList.length) {
                this.$set(this.dataBox, subCategory.id, {
                  selectedItems: [],
                  count: 0
                });
                const matchingServiceIds = this.shifuInfo.serviceIds.filter(serviceId =>
                  subCategory.serviceList.some(service => service.id === serviceId)
                );
                this.dataBox[subCategory.id].selectedItems = matchingServiceIds;
                this.dataBox[subCategory.id].count = matchingServiceIds.length;
              }
            });
          }
        });

        console.log("Updated dataBox after getInfoS:", this.dataBox);
        console.log("Updated shifuInfo.serviceIds after getInfoS:", this.shifuInfo.serviceIds);
        this.$forceUpdate();
      } catch (err) {
        console.error("Error in getInfoS:", err);
        this.shifuInfo = { serviceIds: [] };
        this.loadSavedSelections(); // Fallback to local storage on API failure
      }
    }
  },
  created() {
    // Initialize debounce function here
    this.debouncedSaveSettings = debounce(this.saveSettings, 1000);
  },
  async onLoad() {
    try {
      const city = uni.getStorageSync("city");
      console.log("City:", city);
      // Clear selectedServices to start fresh only if you want to explicitly clear it
      // uni.setStorageSync('selectedServices', ''); 
      await this.getList();
      await this.getInfoS();
    } catch (err) {
      console.error("Error in onLoad:", err);
      uni.showToast({
        title: "页面加载失败",
        icon: "none"
      });
    }
  },
};
</script>

<style scoped>
.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* Reserve space for fixed footer */
  box-sizing: border-box; /* Ensures padding is included in height */
}

.main {
  flex: 1;
  display: flex;
  overflow: hidden; /* Important for scroll-views inside */
}

.left {
  width: 190rpx;
  background-color: #f8f8f8;
  flex-shrink: 0; /* Prevent it from shrinking */
}

.scrollL {
  height: 100%;
  overflow-y: auto;
}

.left_item {
  padding: 0 20rpx;
  min-height: 100rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  border-left: 6rpx solid transparent;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
  justify-content: center; /* Center content vertically */
}

.left_item.active {
  color: #2e80fe;
  font-size: 30rpx;
  border-left-color: #2e80fe;
  background-color: #fff;
}

.category_name {
  height: 100rpx; /* Ensure consistent height for names */
  width: 100%;
  display: flex;
  align-items: center;
}

.right {
  flex: 1;
  background-color: #fff;
  border-radius: 12rpx 12rpx 0 0;
  margin-left: 10rpx;
  overflow: hidden; /* Important for scroll-views inside */
}

.scrollR {
  height: 100%;
  overflow-y: auto;
}

.subcategory_section {
  margin-bottom: 15rpx;
}

.subcategory_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fafafa;
  border-bottom: 1rpx solid #f0f0f0;
}

.subcategory_title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  flex: 1; /* Allow title to take available space */
}

.selected_count {
  color: #2e80fe;
  font-weight: normal;
  margin-left: 10rpx; /* Add some spacing */
}

.select_all {
  font-size: 26rpx;
  color: #2e80fe;
  margin-left: 20rpx;
  flex-shrink: 0; /* Prevent it from shrinking */
}

.expand_icon {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx; /* Add some spacing */
  flex-shrink: 0; /* Prevent it from shrinking */
}

.service_items {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}

.service_item {
  width: calc(33.33% - 20rpx); /* Adjusted for margin */
  margin: 10rpx; /* Apply margin on all sides */
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s;
  text-align: center;
  padding: 0 10rpx;
  box-sizing: border-box; /* Include padding in width calculation */
  white-space: nowrap; /* Prevent text wrapping */
  overflow: hidden; /* Hide overflow text */
  text-overflow: ellipsis; /* Show ellipsis for overflow */
}

.service_item.active {
  background-color: #e6f0ff;
  color: #2e80fe;
  border: 1rpx solid #2e80fe;
}

.no-services,
.no-content,
.loading,
.error {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx;
}

.error {
  color: #ff4d4f;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  padding: 15rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999; /* Increased z-index to ensure visibility */
  box-sizing: border-box; /* Include padding in height calculation */
}

.save_btn {
  width: 90%;
  height: 90rpx;
  background-color: #2e80fe;
  color: white;
  border-radius: 45rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save_btn.disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}
</style>
```