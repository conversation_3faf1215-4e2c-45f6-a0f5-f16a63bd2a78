<template>
	<view class="page">
		<!-- #ifdef APP-ANDROID -->
		<u-popup :show="showImg" mode='top' :round="10">
			<view style="padding: 20rpx 100rpx; background-color: #2a82e4; color: #fff;border-radius:20rpx;">
				<text>使用摄像头中拍摄照片或相册中选择图片用以上传用户所需的图片</text>
			</view>
		</u-popup>
		<!-- #endif -->
		<u-picker
		    v-if="flag"
		    :show="showCity"
		    ref="uPicker"
		    :loading="loading"
		    :columns="columnsCity"
		    @change="changeHandler"
		    keyName="title"
		    @cancel="showCity = false"
		    @confirm="confirmCity"
		></u-picker>
		<u-modal
		    :show="show"
		    :title="title"
		    :showCancelButton="true"
		    confirmText="同意"
		    cancelText="不同意"
		    @confirm="confirmModel"
		    @cancel="cancelModel"
		>
		    <view class="slot-content">
		        <rich-text :nodes="entryNotice"></rich-text>
		    </view>
		</u-modal>
		<u-modal
		    v-if="shInfo.status == 4"
		    :show="showSh"
		    title="驳回原因"
		    confirmText="确定"
		    @confirm="showSh = false"
		    :content="shInfo.shText"
		></u-modal>
		<u-modal
		    :show="showConfirm"
		    title="确认提交"
		    content="确定要提交信息吗？"
		    :showCancelButton="true"
		    confirmText="确定"
		    cancelText="取消"
		    @confirm="handleConfirmSubmit"
		    @cancel="showConfirm = false"
		></u-modal>
		<view
		    class="header"
		    v-if="shInfo.status"
		    :style="'color:' + arr[shInfo.status - 1].color"
		    @click="shDetail"
		>
		    {{ arr[shInfo.status - 1].text }}
		</view>
		<view class="main">
			<u-picker :show="showCity" ref="uPicker" :loading="loading" :columns="columnsCity" @change="changeHandler"
				keyName="title" @cancel="showCity = false" @confirm="confirmCity"></u-picker>
			<view class="main_item">
				<view class="title"><span>*</span>姓名</view>
				<input type="text" v-model="form.coach_name" placeholder="请输入姓名">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>手机号</view>
				<input type="text" v-model="form.mobile" placeholder="请输入手机号">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>性别</view>
				<u-radio-group v-model="form.sex" placement="row">
					<u-radio :customStyle="{marginRight: '20px'}" label="男" :name="0"></u-radio>
					<u-radio label="女" :name="1"></u-radio>
				</u-radio-group>
			</view>
			<!-- <view class="main_item">
				<view class="title"><span>*</span>从业年份</view>
				<input type="text" v-model="form.work_time" placeholder="请输入从业年份">
			</view> -->
			<view class="main_item">
				<view class="title"><span>*</span>所在地址</view>
				<input type="text" v-model="form.address" placeholder="请输入所在地址">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>选择技能</view>
				<input type="text" v-model="serviceDisplayName" placeholder="请选择服务" disabled @click="navigateToSkills">
			</view>
			<!-- <view class="main_item">
				<view class="title"><span>*</span>选择区域</view>
				<input type="text" v-model="form.city" placeholder="请选择代理区域" disabled @click="showCity = true">
			</view> -->
			<!-- <view class="main_item">
				<view class="title">自我介绍(非必填)</view>
				<input type="text" v-model="form.text" placeholder="请输入自我介绍">
			</view> -->
			<view class="main_item">
				<view class="title"><span>*</span>身份证号</view>
				<input type="text" v-model="form.id_code" placeholder="请输入身份证号">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>上传身份证照片</view>
				<view class="card">
					<view class="card_item">
						<view class="top">
							<view class="das">
								<view class="up">
									<upload @upload="imgUpload" :imagelist="form.id_card1" imgtype="id_card1"
										imgclass="id_card_box" text="身份证人像面" :imgsize="1" @changeShow="changeShow"></upload>
								</view>
							</view>
						</view>
						<view class="bottom">拍摄人像面</view>
					</view>
					<view class="card_item">
						<view class="top">
							<view class="das">
								<view class="up">
									<upload @upload="imgUpload" :imagelist="form.id_card2" imgtype="id_card2"
										imgclass="id_card_box" text="身份证国徽面" :imgsize="1" @changeShow="changeShow"></upload>
								</view>
							</view>
						</view>
						<view class="bottom">拍摄国徽面</view>
					</view>
				</view>
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>上传形象照片</view>
				<upload @upload="imgUpload" @del="imgUpload" :imagelist="form.self_img" imgtype="self_img" imgclass=""
					text="形象照片" :imgsize="3" @changeShow="changeShow"></upload>
			</view>
		</view>
		<view class="footer">
			<view class="btn" @click="submit">保存</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			arr: [
				{ text: '信息审核中，请稍作等待', color: '#FE921B' },
				{ text: '审核成功', color: '#07C160' },
				{},
				{ text: '审核失败>点击查看', color: '#E72427' },
			],
			showImg: false,
			showCity: false,
			showSh: false,
			showConfirm: false,
			loading: false,
			shInfo: {},
			columnsCity: [[], [], []],
			form: {
				serviceInfo: '',
				coach_name: '',
				mobile: '',
				sex: 0,
				work_time: '',
				address: '',
				userId: '',
				serviceIds: '',
				serviceInfoName: '',
				text: '',
				city: '',
				city_id: '',
				id_code: '',
				id_card1: [],
				id_card2: [],
				self_img: []
			}
		};
	},
	computed: {
		serviceDisplayName: {
			get() {
				return this.form.serviceInfo || this.serviceInfoName;
			},
			set(value) {
				
				this.form.serviceInfo = value;
			}
		}
	},
	methods: {
		navigateToSkills() {
			uni.navigateTo({
				url: '/shifu/skillsIndex'
			});
		},
		shDetail() {
			if (this.shInfo.status != 4) return;
			this.showSh = true;
		},
		changeShow(e) {
			this.showImg = e;
		},
		getcity(e) {
			this.$api.service.getCity(e).then(res => {
				this.columnsCity[0] = res;
				this.$api.service.getCity(res[0].id).then(res1 => {
					this.columnsCity[1] = res1;
					this.$api.service.getCity(res1[0].id).then(res2 => {
						this.columnsCity[2] = res2;
					});
				});
			});
		},
		changeHandler(e) {
			const { columnIndex, index, picker = this.$refs.uPicker } = e;
			if (columnIndex === 0) {
				this.$api.service.getCity(this.columnsCity[0][index].id).then(res => {
					picker.setColumnValues(1, res);
					this.columnsCity[1] = res;
					this.$api.service.getCity(res[0].id).then(res1 => {
						picker.setColumnValues(2, res1);
						this.columnsCity[2] = res1;
					});
				});
			} else if (columnIndex === 1) {
				this.$api.service.getCity(this.columnsCity[1][index].id).then(res => {
					picker.setColumnValues(2, res);
					this.columnsCity[2] = res;
				});
			}
		},
		confirmCity(Array) {
			this.form.city = Array.value
				.map((item, index) => {
					if (item == undefined) {
						return this.columnsCity[index][0].title;
					} else {
						return item.title;
					}
				})
				.join(',');
			this.form.city_id = Array.value
				.map((e, j) => {
					if (e == undefined) {
						return this.columnsCity[j][0].id;
					} else {
						return e.id;
					}
				})
				.join(',');
			this.showCity = false;
		},
		submit() {
			this.showConfirm = true;
		},
		handleConfirmSubmit() {
			// Check required fields only
			const requiredFields = ['coach_name','self_img','id_card1','id_card2', 'mobile', 'work_time', 'city', 'city_id', 'address', 'id_code'];
			for (let key of requiredFields) {
				if (this.form[key] === '' || (Array.isArray(this.form[key]) && this.form[key].length === 0)) {
					uni.showToast({
						icon: 'none',
						title: '请填写必填项',
					});
					this.showConfirm = false;
					return;
				}
			}

			// Validate ID card format
			let p = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
			if (!p.test(this.form.id_code)) {
				uni.showToast({
					icon: 'none',
					title: '请填写正确的身份证号',
				});
				this.showConfirm = false;
				return;
			}

			// Validate phone number format
			let phoneReg = /^1[3456789]\d{9}$/;
			if (!phoneReg.test(this.form.mobile)) {
				uni.showToast({
					icon: 'none',
					title: '请填写正确的手机号',
					duration: 1000,
				});
				this.showConfirm = false;
				return;
			}

			// Prepare data for submission
			let obj = {
				id: this.form.id || 0,
				coachName: this.form.coach_name,
				mobile: this.form.mobile,
				address: this.form.address,
				city: this.form.city,
				cityId: Array.isArray(this.form.city_id) ? this.form.city_id.join(',') : this.form.city_id, 
				userId: this.form.userId,
				idCode: this.form.id_code,
				serviceIds: uni.getStorageSync('selectedServices') ? uni.getStorageSync('selectedServices') : this.form.serviceIds,
				idCard: [
					this.form.id_card1 && this.form.id_card1[0] && this.form.id_card1[0].path
						? this.form.id_card1[0].path
						: '',
					this.form.id_card2 && this.form.id_card2[0] && this.form.id_card2[0].path
						? this.form.id_card2[0].path
						: ''
				],
				selfImg: Array.isArray(this.form.self_img)
					? this.form.self_img.map(img => img.path || '')
					: [],
				sex: this.form.sex,
				text: this.form.text,
				workTime: parseInt(this.form.work_time),
			};
			
			this.shInfo = { ...this.shInfo, ...obj };
			console.log(this.shInfo);
		
			this.$api.shifu.updataInfoSF(this.shInfo).then(res => {
				if (res.code === "200") {
					uni.showToast({
						icon: 'success',
						title: '保存成功'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 1000);
					uni.removeStorageSync('selectedServiceNames');
					uni.removeStorageSync('selectedServices');
				} else {
					uni.showToast({
						icon: 'none',
						title: '保存失败'
					});
				}
				this.showConfirm = false;
			}).catch(err => {
				uni.showToast({
					icon: 'none',
					title: '网络错误'
				});
				this.showConfirm = false;
			});
		},
		imgUpload(e) {
			let { imagelist, imgtype } = e;
			this.form[imgtype] = imagelist;
		},
		getInfo() {
			this.$api.shifu.getMaster().then(ress => {
				let res=ress.data
				const data = res;
				console.log(res);
				this.shInfo = data;
				this.form = {
					id: data.id || '',
					coach_name: data.coachName || '',
					mobile: data.mobile || '',
					sex: data.sex || 0,
					work_time: data.workTime || '',
					address: data.address || '',
					lng: data.lng || '',
					lat: data.lat || '',
					serviceIds: data.serviceIds || 0,
					text: data.text || '',
					userId: data.userId || '',
					serviceInfo: data.serviceInfo || '',
					city: data.city || '',
					city_id: data.cityId || '',
					id_code: data.idCode || '',
					id_card1: data.idCard && data.idCard[0] ? [{ path: data.idCard[0] }] : [],
					id_card2: data.idCard && data.idCard[1] ? [{ path: data.idCard[1] }] : [],
					self_img: data.selfImg ? data.selfImg.map(path => ({ path })) : []
				};
			});
		}
	},
onUnload() {
  console.log('onUnload triggered');
  uni.$off('getShInfo');
  uni.removeStorageSync('selectedServiceNames');
  uni.removeStorageSync('selectedServices');
},
	onShow() {
		  this.serviceInfoName = uni.getStorageSync("selectedServiceNames") || '';
	},
	onLoad() {
		this.serviceInfoName = uni.getStorageSync("selectedServiceNames");
		let userphone = uni.getStorageSync("userInfo");
		console.log(userphone);
		this.form.mobile = userphone.phone;
		this.serviceInfoName = uni.getStorageSync("selectedServiceNames");
		this.getcity(0);
		this.getInfo();
	}
};
</script>

<style scoped lang="scss">
	.header {
		margin-top: 2rpx;
	    width: 750rpx;
	    height: 58rpx;
	    background: #fff7f1;
	    line-height: 58rpx;
	    text-align: center;
	    font-size: 28rpx;
	    font-weight: 400;
	}
.page {
	padding-bottom: 200rpx;
	min-height: 100vh;
	box-sizing: border-box;

	.header {
		width: 750rpx;
		height: 58rpx;
		background: #FFF7F1;
		line-height: 58rpx;
		text-align: center;
		font-size: 28rpx;
		font-weight: 400;
	}

	.main {
		padding: 40rpx 30rpx;

		.main_item {
			margin-bottom: 20rpx;

			.title {
				margin-bottom: 20rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;

				span {
					color: #E72427;
				}
			}

			input {
				width: 690rpx;
				height: 110rpx;
				background: #F8F8F8;
				font-size: 28rpx;
				font-weight: 400;
				line-height: 110rpx;
				padding: 0 40rpx;
				box-sizing: border-box;
			}

			.card {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.card_item {
					width: 332rpx;
					height: 332rpx;
					background: #F2FAFE;
					border-radius: 16rpx;
					overflow: hidden;

					.top {
						height: 266rpx;
						width: 100%;
						padding-top: 40rpx;

						.das {
							margin: 0 auto;
							width: 266rpx;
							height: 180rpx;
							border: 2rpx dashed #2E80FE;
							padding-top: 28rpx;

							.up {
								margin: 0 auto;
								width: 210rpx;
								height: 130rpx;
							}
						}
					}

					.bottom {
						height: 66rpx;
						width: 100%;
						background-color: #2E80FE;
						font-size: 28rpx;
						font-weight: 400;
						color: #FFFFFF;
						text-align: center;
						line-height: 66rpx;
					}
				}
			}
		}
	}

	.footer {
		padding: 30rpx 30rpx;
		width: 750rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
		position: fixed;
		bottom: 0;
		left: 0;
		z-index: 100;
		padding-bottom: calc(10rpx + env(safe-area-inset-bottom));

		.btn {
			width: 690rpx;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 50rpx;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 98rpx;
			text-align: center;
		}
	}
}
</style>